import { z } from 'zod';
import { BillingInformationSchema } from './client.validator';

export const VatInfoSchema = z.object({
  percent: z.number(),
  reason_without: z.string().nullable(),
});

export const ProblemSchema = z.object({
  title: z.string(),
  description: z.string(),
});

export const SolutionSchema = z.object({
  title: z.string(),
  description: z.string(),
});

export const TimelineItemSchema = z.object({
  title: z.string(),
  description: z.string(),
  duration: z.string(),
});

export const PricingItemSchema = z.object({
  item: z.string(),
  hours: z.number(),
  rate: z.number(),
  total: z.number(),
  added_after_initial_phase: z.boolean().optional(),
});

export const ProposalDraftSchema = z.object({
  project_title: z.string(),
  price: z.number(),
  currency: z.string(),
  problems: z.array(ProblemSchema),
  solutions: z.array(SolutionSchema),
  scope: z.array(z.string()),
  timeline: z.array(TimelineItemSchema),
  pricing: z.array(PricingItemSchema),
  summary: z.array(z.string()),
  billing_information: BillingInformationSchema.nullable(),
});

export const ProposalDraftRequestSchema = z.object({
  client_id: z.string().uuid(),
  hourly_rate: z.number(),
  currency: z.string(),
  prompt: z.string(),
  current_draft: ProposalDraftSchema.nullable(),
});

export const ProposalRequestSchema = z.object({
  project_title: z.string(),
  client_id: z.string().uuid(),
  expiration_date: z.string(),
  hourly_rate: z.number(),
  price: z.number(),
  currency: z.string(),
  problems: z.array(ProblemSchema),
  solutions: z.array(SolutionSchema),
  scope: z.array(z.string()),
  timeline: z.array(TimelineItemSchema),
  pricing: z.array(PricingItemSchema),
  summary: z.array(z.string()),
});

export type VatInfo = z.infer<typeof VatInfoSchema>;
export type ProposalDraft = z.infer<typeof ProposalDraftSchema>;
export type ProposalDraftRequest = z.infer<typeof ProposalDraftRequestSchema>;
export type ProposalRequest = z.infer<typeof ProposalRequestSchema>;
