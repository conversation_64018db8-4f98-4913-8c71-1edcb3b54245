<div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-4">
  <div class="max-w-6xl mx-auto">
    <!-- Loading State for Edit Mode -->
    <div *ngIf="isLoadingProposal" class="flex items-center justify-center min-h-[300px]">
      <div class="flex flex-col items-center gap-3">
        <p-progressSpinner
          [style]="{ width: '40px', height: '40px' }"
          animationDuration="1s"
          fill="transparent"
          strokeWidth="4">
        </p-progressSpinner>
        <p class="text-slate-300 font-medium">Loading proposal for editing...</p>
      </div>
    </div>

    <!-- Main Content -->
    <div *ngIf="!isLoadingProposal">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-white mb-1">
            {{ isEditMode ? 'Edit Proposal' : 'Create New Proposal' }}
          </h1>
          <p *ngIf="isEditMode && currentProposal" class="text-slate-400">
            Editing: {{ currentProposal.project_title }}
          </p>
          <p *ngIf="!isEditMode" class="text-slate-400">
            Create a new business proposal with AI assistance
          </p>
        </div>

        <!-- Back to Dashboard Button -->
        <button
          class="flex items-center gap-2 bg-slate-700 hover:bg-slate-600 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200"
          routerLink="/dashboard">
          <i class="pi pi-arrow-left text-sm"></i>
          <span>Back to Dashboard</span>
        </button>
      </div>

      <!-- Form Section -->
      <div
        class="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 rounded-xl p-5 shadow-2xl border border-slate-600 mb-6">
        <div class="flex items-center gap-3 mb-5">
          <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 p-2 rounded-lg">
            <i class="pi pi-cog text-white"></i>
          </div>
          <h3 class="text-lg font-bold text-indigo-200">
            {{ isEditMode ? 'Proposal Configuration' : 'Project Configuration' }}
          </h3>
        </div>

        <form [formGroup]="generateProposalFormGroup" class="space-y-5">
          <!-- Client Selection Section -->
          <div class="space-y-4">
            <label class="block text-sm font-medium text-indigo-200 mb-3">Client Information</label>
            <div class="flex items-center gap-3">
              <div class="flex-1">
                <p-select
                  [options]="clients"
                  [style]="{ width: '100%' }"
                  class="w-full"
                  formControlName="client_id"
                  inputId="clientDropdown"
                  optionLabel="name"
                  optionValue="id"
                  panelStyleClass="bg-slate-900 text-white"
                  placeholder="Choose a client"
                  styleClass="custom-select">
                </p-select>
              </div>
              <div class="flex gap-2">
                <button
                  (click)="clientDialogVisible = true"
                  class="w-10 h-10 bg-green-500/80 hover:bg-green-500 text-white rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
                  title="Add Client"
                  type="button">
                  <i class="pi pi-user-plus text-sm"></i>
                </button>
                <button
                  (click)="handleEditClient()"
                  [disabled]="!selectedClient"
                  class="w-10 h-10 bg-blue-500/80 hover:bg-blue-500 disabled:bg-slate-500 text-white rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
                  title="Edit Client"
                  type="button">
                  <i class="pi pi-pencil text-sm"></i>
                </button>
                <button
                  (click)="handleDeleteClient()"
                  [disabled]="!selectedClient"
                  class="w-10 h-10 bg-red-500/80 hover:bg-red-500 disabled:bg-slate-500 text-white rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
                  title="Delete Client"
                  type="button">
                  <i class="pi pi-trash text-sm"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Project Details Section -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-indigo-200 mb-2" for="proposalRate">Hourly Rate</label>
              <input
                class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-indigo-400 focus:ring-2 focus:ring-indigo-400/20 transition-all duration-200"
                formControlName="hourly_rate"
                id="proposalRate"
                pInputText
                placeholder="100.00"
                type="number"/>
            </div>
            <div>
              <label class="block text-sm font-medium text-indigo-200 mb-2" for="proposalCurrency">Currency</label>
              <input
                class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-indigo-400 focus:ring-2 focus:ring-indigo-400/20 transition-all duration-200"
                formControlName="currency"
                id="proposalCurrency"
                pInputText
                placeholder="USD"/>
            </div>
            <div>
              <label class="block text-sm font-medium text-indigo-200 mb-2" for="proposalExpiration">Expiration
                Date</label>
              <p-datepicker
                [iconDisplay]="'input'"
                [inputStyle]="{
                width: '100%',
                background: 'rgba(15, 23, 42, 0.5)',
                border: '1px solid rgb(71, 85, 105)',
                borderRadius: '0.5rem',
                padding: '0.5rem 0.75rem',
                color: 'white'
              }"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-mm-dd"
                formControlName="expiration_date"
                inputId="proposalExpiration"
                placeholder="Select date"
                styleClass="custom-calendar">
              </p-datepicker>
            </div>
          </div>

          <!-- AI Prompt Section - Only show in create mode -->
          <div *ngIf="!isEditMode">
            <label class="block text-sm font-medium text-indigo-200 mb-2">Project Description</label>
            <textarea
              class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-indigo-400 focus:ring-2 focus:ring-indigo-400/20 transition-all duration-200 min-h-[120px]"
              formControlName="prompt"
              pTextarea
              placeholder="Describe the project or goal for the proposal..."
              rows="6">
          </textarea>
            <div class="mt-4 flex justify-center">
              <button
                (click)="handleGenerateProposalDraft()"
                [disabled]="generateProposalFormGroup.invalid || isGeneratingDraft"
                class="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-500 disabled:to-gray-600 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25">
                <i class="pi pi-magic-wand text-sm"></i>
                <span>Generate Proposal Draft</span>
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Edit Mode Message -->
      <div *ngIf="isEditMode" class="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6">
        <div class="flex items-center gap-3">
          <i class="pi pi-info-circle text-blue-400 text-lg"></i>
          <div>
            <h4 class="text-blue-200 font-semibold">Edit Mode</h4>
            <p class="text-blue-300 text-sm">You're editing an existing proposal. Make changes to the sections below and
              click "Update Proposal" to save.</p>
          </div>
        </div>
      </div>

      <!-- Loading Spinner -->
      <div *ngIf="isGeneratingDraft" class="flex flex-col items-center justify-center mt-4 mb-4">
        <p-progressSpinner
          [style]="{ width: '50px', height: '50px' }"
          animationDuration="1s"
          fill="transparent"
          strokeWidth="4"
          styleClass="custom-spinner">
        </p-progressSpinner>
        <p class="mt-2 text-purple-300 font-medium">Generating your proposal draft...</p>
      </div>
      <div *ngIf="proposalDraft" class="mt-6 space-y-6">
        <!-- Project Overview Section -->
        <div
          class="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 rounded-xl p-5 shadow-2xl border border-slate-600">
          <div class="flex items-center gap-3 mb-5">
            <div class="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-lg">
              <i class="pi pi-file-edit text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-purple-100">Project Overview</h3>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-purple-200 mb-2">Project Title</label>
              <input [(ngModel)]="proposalDraft.project_title" [ngModelOptions]="{standalone: true}"
                     class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
                     pInputText placeholder="Enter project title"/>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-purple-200 mb-2">Price</label>
                <input [(ngModel)]="proposalDraft.price" [ngModelOptions]="{standalone: true}"
                       class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
                       pInputText placeholder="0.00" type="number"/>
              </div>
              <div>
                <label class="block text-sm font-medium text-purple-200 mb-2">Currency</label>
                <input [(ngModel)]="proposalDraft.currency" [ngModelOptions]="{standalone: true}"
                       class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
                       pInputText placeholder="USD"/>
              </div>
            </div>
          </div>
        </div>

        <!-- Problems Section -->
        <div
          class="bg-gradient-to-r from-red-900/20 via-red-800/20 to-red-900/20 rounded-xl p-6 shadow-2xl border border-red-500/30 backdrop-blur-sm">
          <div class="flex items-center gap-3 mb-6">
            <div class="bg-gradient-to-r from-red-500 to-red-600 p-2 rounded-lg">
              <i class="pi pi-exclamation-triangle text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-indigo-200">Problems & Challenges</h3>
          </div>

          <div class="space-y-4">
            <div *ngFor="let p of proposalDraft.problems; let i = index"
                 class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-red-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10 relative">
              <div class="space-y-3">
                <div>
                  <label class="block text-sm font-medium text-red-200 mb-2">Problem Title</label>
                  <input [(ngModel)]="p.title" [ngModelOptions]="{standalone: true}"
                         class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 transition-all duration-200"
                         pInputText placeholder="Describe the main problem"/>
                </div>
                <div>
                  <label class="block text-sm font-medium text-red-200 mb-2">Problem Description</label>
                  <textarea [(ngModel)]="p.description" [ngModelOptions]="{standalone: true}"
                            class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-red-400 focus:ring-2 focus:ring-red-400/20 transition-all duration-200 min-h-[100px]"
                            pInputTextarea
                            placeholder="Provide detailed explanation of the problem and its impact"></textarea>
                </div>
              </div>
              <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                <button (click)="proposalDraft.problems.splice(i, 1)"
                        aria-label="Remove Problem"
                        class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                  <i class="pi pi-trash text-xs"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-center">
            <button (click)="proposalDraft.problems.push({ title: '', description: '' })"
                    class="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-red-500/25 hover:-translate-y-0.5">
              <i class="pi pi-plus text-sm"></i>
              <span>Add Problem</span>
            </button>
          </div>
        </div>

        <!-- Solutions Section -->
        <div
          class="bg-gradient-to-r from-green-900/20 via-green-800/20 to-green-900/20 rounded-xl p-6 shadow-2xl border border-green-500/30 backdrop-blur-sm">
          <div class="flex items-center gap-3 mb-6">
            <div class="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg">
              <i class="pi pi-lightbulb text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-indigo-200">Solutions & Approach</h3>
          </div>

          <div class="space-y-4">
            <div *ngFor="let s of proposalDraft.solutions; let i = index"
                 class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-green-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/10 relative">
              <div class="space-y-3">
                <div>
                  <label class="block text-sm font-medium text-green-200 mb-2">Solution Title</label>
                  <input [(ngModel)]="s.title" [ngModelOptions]="{standalone: true}"
                         class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 transition-all duration-200"
                         pInputText placeholder="Name your solution approach"/>
                </div>
                <div>
                  <label class="block text-sm font-medium text-green-200 mb-2">Solution Description</label>
                  <textarea [(ngModel)]="s.description" [ngModelOptions]="{standalone: true}"
                            class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 transition-all duration-200 min-h-[100px]"
                            pInputTextarea
                            placeholder="Explain how this solution addresses the problem and its benefits"></textarea>
                </div>
              </div>
              <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                <button (click)="proposalDraft.solutions.splice(i, 1)"
                        aria-label="Remove Solution"
                        class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                  <i class="pi pi-trash text-xs"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-center">
            <button (click)="proposalDraft.solutions.push({ title: '', description: '' })"
                    class="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-green-500/25 hover:-translate-y-0.5">
              <i class="pi pi-plus text-sm"></i>
              <span>Add Solution</span>
            </button>
          </div>
        </div>

        <!-- Scope Section -->
        <div
          class="bg-gradient-to-r from-blue-900/20 via-blue-800/20 to-blue-900/20 rounded-xl p-6 shadow-2xl border border-blue-500/30 backdrop-blur-sm">
          <div class="flex items-center gap-3 mb-6">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
              <i class="pi pi-list text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-indigo-200">Project Scope</h3>
          </div>

          <div class="space-y-4">
            <div *ngFor="let s of proposalDraft.scope; let i = index"
                 class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-blue-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10 relative">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-1">
                  <span class="text-white text-xs font-bold">{{ i + 1 }}</span>
                </div>
                <div class="flex-1">
                  <label class="block text-sm font-medium text-blue-200 mb-2">Scope Item</label>
                  <textarea [(ngModel)]="proposalDraft.scope[i]" [ngModelOptions]="{standalone: true}"
                            class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 transition-all duration-200 min-h-[80px]"
                            pInputTextarea placeholder="Describe what's included in this scope item"></textarea>
                </div>
                <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button (click)="proposalDraft.scope.splice(i, 1)"
                          aria-label="Remove scope item"
                          class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                    <i class="pi pi-trash text-xs"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-center">
            <button (click)="proposalDraft.scope.push('')"
                    class="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-0.5">
              <i class="pi pi-plus text-sm"></i>
              <span>Add Scope Item</span>
            </button>
          </div>
        </div>

        <!-- Timeline Section -->
        <div
          class="bg-gradient-to-r from-orange-900/20 via-orange-800/20 to-orange-900/20 rounded-xl p-6 shadow-2xl border border-orange-500/30 backdrop-blur-sm">
          <div class="flex items-center gap-3 mb-6">
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-2 rounded-lg">
              <i class="pi pi-calendar text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-indigo-200">Project Timeline</h3>
          </div>

          <div class="space-y-4">
            <div *ngFor="let t of proposalDraft.timeline; let i = index"
                 class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-orange-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/10 relative">
              <div class="flex items-start gap-4">
                <div class="flex-shrink-0 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <i class="pi pi-clock text-white text-sm"></i>
                </div>
                <div class="flex-1 space-y-3">
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-orange-200 mb-2">Phase Title</label>
                      <input [(ngModel)]="t.title" [ngModelOptions]="{standalone: true}"
                             class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                             pInputText placeholder="Phase or milestone name"/>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-orange-200 mb-2">Duration</label>
                      <input [(ngModel)]="t.duration" [ngModelOptions]="{standalone: true}"
                             class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                             pInputText placeholder="e.g., 2 weeks, 1 month"/>
                    </div>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-orange-200 mb-2">Description</label>
                    <textarea [(ngModel)]="t.description" [ngModelOptions]="{standalone: true}"
                              class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200 min-h-[80px]"
                              pInputTextarea placeholder="Describe what will be accomplished in this phase"></textarea>
                  </div>
                </div>
                <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button (click)="proposalDraft.timeline.splice(i, 1)"
                          aria-label="Remove Timeline Item"
                          class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                    <i class="pi pi-trash text-xs"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-center">
            <button (click)="proposalDraft.timeline.push({ title: '', duration: '', description: '' })"
                    class="flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-orange-500/25 hover:-translate-y-0.5">
              <i class="pi pi-plus text-sm"></i>
              <span>Add Timeline Item</span>
            </button>
          </div>
        </div>

        <!-- Pricing Section -->
        <div
          class="bg-gradient-to-r from-emerald-900/20 via-emerald-800/20 to-emerald-900/20 rounded-xl p-6 shadow-2xl border border-emerald-500/30 backdrop-blur-sm">
          <div class="flex items-center gap-3 mb-6">
            <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 p-2 rounded-lg">
              <i class="pi pi-dollar text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-indigo-200">Pricing Breakdown</h3>
          </div>

          <!-- Initial Phase Items -->
          <div class="mb-8">
            <div class="flex items-center gap-2 mb-4">
              <h4 class="text-lg font-semibold text-emerald-300">Initial Phase</h4>
              <span class="text-sm text-emerald-400 bg-emerald-900/30 px-2 py-1 rounded">
                {{ initialPhaseTotal | currency:(proposalDraft.currency || 'BGN') }}
              </span>
            </div>

            <div class="space-y-4">
              <div *ngFor="let p of initialPhaseItems; let i = index"
                   class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-emerald-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/10 relative">
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-emerald-200 mb-2">Service/Item</label>
                    <input [(ngModel)]="p.item" [ngModelOptions]="{standalone: true}"
                           class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                           pInputText placeholder="Service or deliverable name"/>
                  </div>
                  <div class="grid grid-cols-3 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-emerald-200 mb-2">Hours</label>
                      <input (ngModelChange)="calculateItemTotal(p)" [(ngModel)]="p.hours"
                             [ngModelOptions]="{standalone: true}"
                             class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                             pInputText placeholder="0" type="number"/>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-emerald-200 mb-2">Rate/hr</label>
                      <input (ngModelChange)="calculateItemTotal(p)" [(ngModel)]="p.rate"
                             [ngModelOptions]="{standalone: true}"
                             class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                             pInputText placeholder="0.00" type="number"/>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-emerald-200 mb-2">Total</label>
                      <div class="relative">
                        <input (ngModelChange)="calculateTotalPrice()" [(ngModel)]="p.total"
                               [ngModelOptions]="{standalone: true}"
                               class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200"
                               pInputText placeholder="0.00" type="number"/>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                          <i class="pi pi-calculator text-emerald-400 text-sm"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button (click)="removePricingItem(proposalDraft.pricing.indexOf(p))"
                          aria-label="Remove Pricing Item"
                          class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                    <i class="pi pi-trash text-xs"></i>
                  </button>
                </div>
              </div>
            </div>

            <div class="mt-4 flex justify-center">
              <button (click)="addPricingItem(false)"
                      class="flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-emerald-500/25 hover:-translate-y-0.5">
                <i class="pi pi-plus text-sm"></i>
                <span>Add Initial Phase Item</span>
              </button>
            </div>
          </div>

          <!-- Overtime Items -->
          <div class="border-t border-orange-500/30 pt-6">
            <div class="flex items-center gap-2 mb-4">
              <h4 class="text-lg font-semibold text-orange-300">Overtime / Additional Work</h4>
              <span class="text-sm text-orange-400 bg-orange-900/30 px-2 py-1 rounded">
                {{ overtimeTotal | currency:(proposalDraft?.currency || 'BGN') }}
              </span>
              <span class="text-xs text-orange-400/70 bg-orange-900/20 px-2 py-1 rounded">
                Added after initial phase
              </span>
            </div>

            <div *ngIf="overtimeItems.length > 0" class="space-y-4">
              <div *ngFor="let p of overtimeItems; let i = index"
                   class="group bg-orange-900/20 backdrop-blur-sm border border-orange-600/50 rounded-lg p-5 hover:border-orange-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/10 relative">
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-orange-200 mb-2">Service/Item</label>
                    <input [(ngModel)]="p.item" [ngModelOptions]="{standalone: true}"
                           class="w-full bg-slate-900/50 border border-orange-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                           pInputText placeholder="Additional service or deliverable"/>
                  </div>
                  <div class="grid grid-cols-3 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-orange-200 mb-2">Hours</label>
                      <input (ngModelChange)="calculateItemTotal(p)" [(ngModel)]="p.hours"
                             [ngModelOptions]="{standalone: true}"
                             class="w-full bg-slate-900/50 border border-orange-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                             pInputText placeholder="0" type="number"/>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-orange-200 mb-2">Rate/hr</label>
                      <input (ngModelChange)="calculateItemTotal(p)" [(ngModel)]="p.rate"
                             [ngModelOptions]="{standalone: true}"
                             class="w-full bg-slate-900/50 border border-orange-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                             pInputText placeholder="0.00" type="number"/>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-orange-200 mb-2">Total</label>
                      <div class="relative">
                        <input (ngModelChange)="calculateTotalPrice()" [(ngModel)]="p.total"
                               [ngModelOptions]="{standalone: true}"
                               class="w-full bg-slate-900/50 border border-orange-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-200"
                               pInputText placeholder="0.00" type="number"/>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                          <i class="pi pi-calculator text-orange-400 text-sm"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button (click)="removePricingItem(proposalDraft.pricing.indexOf(p))"
                          aria-label="Remove Overtime Item"
                          class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                    <i class="pi pi-trash text-xs"></i>
                  </button>
                </div>
              </div>
            </div>

            <div class="mt-4 flex justify-center">
              <button (click)="addPricingItem(true)"
                      class="flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-orange-500/25 hover:-translate-y-0.5">
                <i class="pi pi-plus text-sm"></i>
                <span>Add Overtime Item</span>
              </button>
            </div>
          </div>

          <!-- Total Summary -->
          <div class="mt-6 pt-4 border-t border-slate-600/50">
            <div class="flex justify-between items-center text-lg font-semibold">
              <span class="text-indigo-200">Total Project Cost:</span>
              <span
                class="text-2xl text-emerald-400">{{ (proposalDraft?.price || 0) | currency:(proposalDraft?.currency || 'BGN') }}</span>
            </div>
          </div>
        </div>

        <!-- Summary Section -->
        <div
          class="bg-gradient-to-r from-violet-900/20 via-violet-800/20 to-violet-900/20 rounded-xl p-6 shadow-2xl border border-violet-500/30 backdrop-blur-sm">
          <div class="flex items-center gap-3 mb-6">
            <div class="bg-gradient-to-r from-violet-500 to-violet-600 p-2 rounded-lg">
              <i class="pi pi-check-circle text-white text-lg"></i>
            </div>
            <h3 class="text-xl font-bold text-indigo-200">Project Summary</h3>
          </div>

          <div class="space-y-4">
            <div *ngFor="let s of proposalDraft.summary; let i = index"
                 class="group bg-slate-800/60 backdrop-blur-sm border border-slate-600/50 rounded-lg p-5 hover:border-violet-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10 relative">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-6 h-6 bg-violet-500 rounded-full flex items-center justify-center mt-1">
                  <i class="pi pi-check text-white text-xs"></i>
                </div>
                <div class="flex-1">
                  <label class="block text-sm font-medium text-violet-200 mb-2">Summary Point</label>
                  <input [(ngModel)]="proposalDraft.summary[i]" [ngModelOptions]="{standalone: true}"
                         class="w-full bg-slate-900/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-violet-400 focus:ring-2 focus:ring-violet-400/20 transition-all duration-200"
                         pInputText placeholder="Key takeaway or benefit"/>
                </div>
                <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button (click)="proposalDraft.summary.splice(i, 1)"
                          aria-label="Remove Summary Point"
                          class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg">
                    <i class="pi pi-trash text-xs"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-center">
            <button (click)="proposalDraft.summary.push('')"
                    class="flex items-center gap-2 bg-gradient-to-r from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-violet-500/25 hover:-translate-y-0.5">
              <i class="pi pi-plus text-sm"></i>
              <span>Add Summary Point</span>
            </button>
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-8">
        <div *ngIf="proposalDraft" class="flex flex-col items-center gap-4">
          <button
            (click)="handleCreateProposal()"
            [disabled]="isCreatingProposal"
            class="flex items-center gap-3 bg-slate-800 hover:bg-slate-700 disabled:bg-slate-500 text-white font-bold px-10 py-4 rounded-lg border-2 border-slate-600 hover:border-slate-500 disabled:border-slate-400 transition-all duration-200 hover:shadow-xl shadow-slate-900/50 disabled:hover:shadow-none uppercase tracking-wide text-sm">
            <i *ngIf="!isCreatingProposal" class="pi pi-file-check text-lg"></i>
            <p-progressSpinner
              *ngIf="isCreatingProposal"
              [style]="{ width: '18px', height: '18px' }"
              animationDuration="1.5s"
              fill="transparent"
              strokeWidth="3">
            </p-progressSpinner>
            <span>{{ isCreatingProposal ? (isEditMode ? 'Updating Proposal...' : 'Creating Proposal...') : (isEditMode ? 'Update Proposal' : 'Create Proposal') }}</span>
          </button>

          <p *ngIf="isCreatingProposal" class="text-slate-400 text-sm font-medium">
            Finalizing and saving proposal to database...
          </p>
        </div>
      </div>
    </div>
  </div>
</div>


<p-dialog [(visible)]="clientDialogVisible" [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
          [draggable]="false"
          [header]="editMode ? 'Edit Client' : 'Create Client'" [modal]="true" [resizable]="false"
          [style]="{ width: '50vw' }">
  <span class="p-text-secondary block mb-6">Enter details for the client.</span>

  <form [formGroup]="clientForm">
    <div class="flex items-center gap-4 mb-4">
      <label class="font-semibold w-24 mr-5" for="clientName">Name</label>
      <input class="flex-auto" formControlName="name" id="clientName" pInputText placeholder="Acme Corp"/>
    </div>
    <div class="flex items-center gap-4 mb-4">
      <label class="font-semibold w-24 mr-5" for="clientEmail">Email</label>
      <input class="flex-auto" formControlName="email" id="clientEmail" pInputText placeholder="<EMAIL>"/>
    </div>
    <div class="flex items-center gap-4 mb-6">
      <label class="font-semibold w-24 mr-5" for="clientRep">Representative</label>
      <input class="flex-auto" formControlName="rep_full_name" id="clientRep" pInputText placeholder="Jane Doe"/>
    </div>

    <div class="flex justify-end gap-2">
      <p-button (click)="clientDialogVisible = false" label="Cancel" severity="secondary"/>
      <p-button (click)="handleCreateClient()" [disabled]="clientForm.invalid"
                [label]="editMode ? 'Save' : 'Create'"/>
    </div>
  </form>
</p-dialog>
