-- Add support for overtime pricing items
-- This migration adds the ability to mark pricing items as added after the initial phase

-- Since pricing is stored as JSONB, we don't need to modify the table structure
-- The new field 'added_after_initial_phase' will be stored within the existing pricing JSONB column

-- Add comment to document the new field structure
COMMENT ON COLUMN proposals.pricing IS 'JSONB array of pricing items. Each item contains: item (string), hours (number), rate (number), total (number), and optionally added_after_initial_phase (boolean) for overtime items added in second phase';
